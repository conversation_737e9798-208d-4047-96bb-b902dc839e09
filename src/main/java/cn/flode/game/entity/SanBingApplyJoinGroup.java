package cn.flode.game.entity;

import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.framework.BaseEntity;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import lombok.*;

/**
 * 申请加入分组类型实体类。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table("san_bing_apply_join_group")
public class SanBingApplyJoinGroup extends BaseEntity implements Serializable {

  /**
   * 联盟ID
   */
  private Long coalitionId;

  /**
   * 成员ID
   */
  private Long memberId;

  /**
   * 分组类型
   */
  private GroupType type;
}
