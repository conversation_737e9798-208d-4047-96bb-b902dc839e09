package cn.flode.game.controller.sanbing.vo;

import cn.flode.game.enums.sanbing.GroupType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "联盟详细信息")
public class CoalitionDetailInfo {

    @Schema(description = "联盟ID", example = "123456")
    private Long coalitionId;
    
    @Schema(description = "联盟名称", example = "无敌联盟")
    private String name;
    
    @Schema(description = "联盟代码", example = "ABC123")
    private String code;
    
    @Schema(description = "区号", example = "1")
    private Integer areaCode;
    
    @Schema(description = "管理员ID", example = "789012")
    private Long managerId;
    
    @Schema(description = "是否为管理员", example = "true")
    private Boolean isManager;
    
    @Schema(description = "按分组类型分组的成员信息")
    private Map<GroupType, List<GroupInfo>> groupsByType;

    @Schema(description = "按申请分组类型分组的成员信息")
    private Map<GroupType, List<MemberInfo>> applyMembersByType;
    
    @Schema(description = "待审核的成员列表（仅管理员可见）")
    private List<MemberInfo> pendingMembers;
}
