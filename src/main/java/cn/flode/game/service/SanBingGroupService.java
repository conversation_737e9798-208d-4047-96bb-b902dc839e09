package cn.flode.game.service;

import cn.flode.game.controller.sanbing.dto.GroupCreateDTO;
import cn.flode.game.controller.sanbing.dto.GroupUpdateDTO;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.entity.SanBingGroup;
import cn.flode.game.entity.SanBingGroupAccount;
import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.mapper.SanBingGroupMapper;
import cn.flode.game.mapper.SanBingGroupAccountMapper;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingGroupService extends ServiceImpl<SanBingGroupMapper, SanBingGroup> {

    private final SanBingCoalitionService coalitionService;
    private final SanBingGroupAccountMapper groupAccountMapper;

    /**
     * 创建联盟分组
     */
    public Long createGroup(GroupCreateDTO createDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        // 检查是否为联盟管理者
        SanBingCoalition coalition = coalitionService.getById(createDTO.getCoalitionId());
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以创建分组");
        }

        // 创建分组
        SanBingGroup group = SanBingGroup.builder()
            .coalitionId(createDTO.getCoalitionId())
            .name(createDTO.getName())
            .type(createDTO.getType())
            .task(createDTO.getTask())
            .build();

        save(group);
        return group.getId();
    }

    /**
     * 更新联盟分组
     */
    public void updateGroup(GroupUpdateDTO updateDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingGroup group = getById(updateDTO.getGroupId());
        if (group == null) {
            throw new RuntimeException("分组不存在");
        }

        // 检查是否为联盟管理者
        SanBingCoalition coalition = coalitionService.getById(group.getCoalitionId());
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以更新分组");
        }

        // 更新分组信息
        group.setName(updateDTO.getName());
        group.setTask(updateDTO.getTask());
        updateById(group);
    }

    /**
     * 删除联盟分组
     */
    public void deleteGroup(Long groupId) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingGroup group = getById(groupId);
        if (group == null) {
            throw new RuntimeException("分组不存在");
        }

        // 检查是否为联盟管理者
        SanBingCoalition coalition = coalitionService.getById(group.getCoalitionId());
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以删除分组");
        }

        // 清空分组内所有成员
        groupMemberMapper.deleteByQuery(
            QueryWrapper.create().eq(SanBingGroupMember::getGroupId, groupId)
        );

        // 删除分组
        removeById(groupId);
    }

    /**
     * 清空某个类型分组的所有成员
     */
    public void clearGroupTypeMembers(Long coalitionId, GroupType groupType) {
        Long currentUserId = SecurityUtils.currentUserId();

        // 检查是否为联盟管理者
        SanBingCoalition coalition = coalitionService.getById(coalitionId);
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以清空分组成员");
        }

        // 查找该类型的所有分组
        List<SanBingGroup> groups = mapper.selectListByQuery(
            QueryWrapper.create()
                .eq(SanBingGroup::getCoalitionId, coalitionId)
                .eq(SanBingGroup::getType, groupType)
        );

        // 清空所有分组的成员
        for (SanBingGroup group : groups) {
            groupMemberMapper.deleteByQuery(
                QueryWrapper.create().eq(SanBingGroupMember::getGroupId, group.getId())
            );
        }
    }
}
