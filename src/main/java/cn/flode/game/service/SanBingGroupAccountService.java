package cn.flode.game.service;

import cn.flode.game.controller.sanbing.dto.AccountAddToGroupDTO;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.entity.SanBingGroup;
import cn.flode.game.entity.SanBingGroupAccount;
import cn.flode.game.entity.SanBingAccount;
import cn.flode.game.entity.SanBingApplyJoinGroup;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.mapper.SanBingApplyJoinGroupMapper;
import cn.flode.game.mapper.SanBingGroupAccountMapper;
import cn.flode.game.mapper.SanBingGroupMapper;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *  分组账号服务层实现。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingGroupAccountService extends ServiceImpl<SanBingGroupAccountMapper, SanBingGroupAccount> {

    private final SanBingCoalitionService coalitionService;
    private final SanBingAccountService accountService;
    private final SanBingGroupMapper groupMapper;
    private final SanBingApplyJoinGroupMapper applyJoinGroupMapper;

    /**
     * 将账号添加到分组
     */
    public void addAccountToGroup(AccountAddToGroupDTO addDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        // 检查分组是否存在
        SanBingGroup group = groupMapper.selectOneById(addDTO.getGroupId());
        if (group == null) {
            throw new RuntimeException("分组不存在");
        }

        // 检查是否为联盟管理者
        SanBingCoalition coalition = coalitionService.getById(group.getCoalitionId());
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以添加账号到分组");
        }

        // 检查账号是否存在且属于该联盟
        SanBingAccount account = accountService.getById(addDTO.getAccountId());
        if (account == null) {
            throw new RuntimeException("账号不存在");
        }
        if (!coalition.getId().equals(account.getCoalitionId()) ||
            account.getStatus() != ApproveStatus.APPROVED) {
            throw new RuntimeException("账号不属于该联盟或未通过审核");
        }

        // 检查账号是否已在该分组
        long existCount = mapper.selectCountByQuery(
            QueryWrapper.create()
                .eq(SanBingGroupAccount::getGroupId, addDTO.getGroupId())
                .eq(SanBingGroupAccount::getAccountId, addDTO.getAccountId())
        );
        if (existCount > 0) {
            throw new RuntimeException("账号已在该分组中");
        }

        // 处理GUAN_DU类型的特殊逻辑（GUAN_DU1和GUAN_DU2是同一种类型）
        if (group.getType() == GroupType.GUAN_DU1 || group.getType() == GroupType.GUAN_DU2) {
            // 查找账号是否已在其他GUAN_DU类型分组中
            List<SanBingGroupAccount> existingGuanDuAccounts = mapper.selectListByQuery(
                QueryWrapper.create()
                    .eq(SanBingGroupAccount::getAccountId, addDTO.getAccountId())
                    .in(SanBingGroup::getType, GroupType.GUAN_DU1, GroupType.GUAN_DU2)
                    .leftJoin(SanBingGroup.class).on(SanBingGroupAccount::getGroupId, SanBingGroup::getId)
            );

            // 如果账号已在其他GUAN_DU类型分组中，先移除
            for (SanBingGroupAccount existingAccount : existingGuanDuAccounts) {
                // 获取现有分组信息
                SanBingGroup existingGroup = groupMapper.selectOneById(existingAccount.getGroupId());
                if (existingGroup != null && !existingGroup.getId().equals(addDTO.getGroupId())) {
                    // 确保现有分组也属于同一联盟
                    if (existingGroup.getCoalitionId().equals(coalition.getId())) {
                        // 从现有GUAN_DU分组中移除
                        mapper.deleteByQuery(
                            QueryWrapper.create()
                                .eq(SanBingGroupAccount::getGroupId, existingGroup.getId())
                                .eq(SanBingGroupAccount::getAccountId, addDTO.getAccountId())
                        );
                    }
                }
            }
        }

        // 添加账号到分组
        SanBingGroupAccount groupAccount = SanBingGroupAccount.builder()
            .groupId(addDTO.getGroupId())
            .accountId(addDTO.getAccountId())
            .build();
        save(groupAccount);

        // 删除相关的申请记录
        GroupType targetType = group.getType();
        if (targetType == GroupType.GUAN_DU1 || targetType == GroupType.GUAN_DU2) {
            // 删除GUAN_DU类型的申请记录
            applyJoinGroupMapper.deleteByQuery(
                QueryWrapper.create()
                    .eq(SanBingApplyJoinGroup::getCoalitionId, coalition.getId())
                    .eq(SanBingApplyJoinGroup::getAccountId, addDTO.getAccountId())
                    .in(SanBingApplyJoinGroup::getType, GroupType.GUAN_DU1, GroupType.GUAN_DU2)
            );
        } else {
            // 删除具体类型的申请记录
            applyJoinGroupMapper.deleteByQuery(
                QueryWrapper.create()
                    .eq(SanBingApplyJoinGroup::getCoalitionId, coalition.getId())
                    .eq(SanBingApplyJoinGroup::getAccountId, addDTO.getAccountId())
                    .eq(SanBingApplyJoinGroup::getType, targetType)
            );
        }
    }



    /**
     * 清空分组账号
     */
    public void clearGroupAccounts(Long groupId) {
        mapper.deleteByQuery(
            QueryWrapper.create().eq(SanBingGroupAccount::getGroupId, groupId)
        );
    }

}
