package cn.flode.game.service;

import cn.flode.game.controller.sanbing.dto.AccountApplyGroupDTO;
import cn.flode.game.entity.SanBingApplyJoinGroup;
import cn.flode.game.entity.SanBingGroup;
import cn.flode.game.entity.SanBingGroupAccount;
import cn.flode.game.entity.SanBingAccount;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.mapper.SanBingApplyJoinGroupMapper;
import cn.flode.game.mapper.SanBingGroupAccountMapper;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 申请加入分组服务层实现。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingApplyJoinGroupService extends ServiceImpl<SanBingApplyJoinGroupMapper, SanBingApplyJoinGroup> {

    private final SanBingAccountService accountService;
    private final SanBingGroupAccountMapper groupAccountMapper;

    /**
     * 账号申请加入分组类型
     */
    public void applyJoinGroupType(AccountApplyGroupDTO applyDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        // 检查账号是否存在且属于当前用户
        SanBingAccount account = accountService.getById(applyDTO.getAccountId());
        if (account == null) {
            throw new RuntimeException("账号不存在");
        }
        if (!currentUserId.equals(account.getCreator())) {
            throw new RuntimeException("只能操作自己创建的账号");
        }
        if (account.getCoalitionId() == null || account.getStatus() != ApproveStatus.APPROVED) {
            throw new RuntimeException("账号未加入联盟或未通过审核");
        }

        GroupType targetType = applyDTO.getGroupType();

        // 检查是否已在该类型的分组中
        if (isInGroupType(account.getId(), targetType)) {
            throw new RuntimeException("账号已在该类型的分组中，无需申请");
        }

        // 检查是否已申请过该类型
        if (targetType == GroupType.GUAN_DU1 || targetType == GroupType.GUAN_DU2) {
            // GUAN_DU1和GUAN_DU2是同一种类型，检查是否已申请过任一种
            long existCount = mapper.selectCountByQuery(
                QueryWrapper.create()
                    .eq(SanBingApplyJoinGroup::getCoalitionId, account.getCoalitionId())
                    .eq(SanBingApplyJoinGroup::getAccountId, account.getId())
                    .in(SanBingApplyJoinGroup::getType, GroupType.GUAN_DU1, GroupType.GUAN_DU2)
            );
            if (existCount > 0) {
                throw new RuntimeException("已申请过官渡类型分组，无法重复申请");
            }
        } else {
            long existCount = mapper.selectCountByQuery(
                QueryWrapper.create()
                    .eq(SanBingApplyJoinGroup::getCoalitionId, account.getCoalitionId())
                    .eq(SanBingApplyJoinGroup::getAccountId, account.getId())
                    .eq(SanBingApplyJoinGroup::getType, targetType)
            );
            if (existCount > 0) {
                throw new RuntimeException("已申请过该类型分组，无法重复申请");
            }
        }

        // 创建申请记录
        SanBingApplyJoinGroup apply = SanBingApplyJoinGroup.builder()
            .coalitionId(account.getCoalitionId())
            .accountId(account.getId())
            .type(targetType)
            .build();
        save(apply);
    }

    /**
     * 检查账号是否已在指定类型的分组中
     */
    private boolean isInGroupType(Long accountId, GroupType groupType) {
        if (groupType == GroupType.GUAN_DU1 || groupType == GroupType.GUAN_DU2) {
            // 检查是否在任一GUAN_DU类型分组中
            long count = groupAccountMapper.selectCountByQuery(
                QueryWrapper.create()
                    .eq(SanBingGroupAccount::getAccountId, accountId)
                    .in(SanBingGroup::getType, GroupType.GUAN_DU1, GroupType.GUAN_DU2)
                    .leftJoin(SanBingGroup.class).on(SanBingGroupAccount::getGroupId, SanBingGroup::getId)
            );
            return count > 0;
        } else {
            // 检查是否在指定类型分组中
            long count = groupAccountMapper.selectCountByQuery(
                QueryWrapper.create()
                    .eq(SanBingGroupAccount::getAccountId, accountId)
                    .eq(SanBingGroup::getType, groupType)
                    .leftJoin(SanBingGroup.class).on(SanBingGroupAccount::getGroupId, SanBingGroup::getId)
            );
            return count > 0;
        }
    }
}
