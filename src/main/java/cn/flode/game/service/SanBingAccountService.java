package cn.flode.game.service;

import cn.flode.game.controller.sanbing.dto.AccountCreateDTO;
import cn.flode.game.controller.sanbing.dto.AccountJoinCoalitionDTO;
import cn.flode.game.controller.sanbing.dto.AccountUpdateDTO;
import cn.flode.game.controller.sanbing.vo.CoalitionSimpleInfo;
import cn.flode.game.controller.sanbing.vo.AccountCreateResult;
import cn.flode.game.controller.sanbing.vo.AccountInfo;
import cn.flode.game.entity.SanBingAccount;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.entity.SanBingGroupAccount;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.mapper.SanBingAccountMapper;
import cn.flode.game.mapper.SanBingGroupAccountMapper;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *  账号服务层实现。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingAccountService extends ServiceImpl<SanBingAccountMapper, SanBingAccount> {

    private final SanBingCoalitionService coalitionService;
    private final SanBingGroupAccountMapper groupAccountMapper;

    /**
     * 创建账号
     */
    public AccountCreateResult createAccount(AccountCreateDTO createDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        // 检查用户是否已创建了2个账号（不限区域）
        long accountCount = mapper.selectCountByQuery(
            QueryWrapper.create().eq(SanBingAccount::getCreator, currentUserId)
                .eq(SanBingAccount::getAreaCode, createDTO.getAreaCode())
        );

        if (accountCount >= 2) {
            throw new RuntimeException("一个区最多只能创建2个账号");
        }

        // 创建账号
        SanBingAccount account = SanBingAccount.builder()
            .areaCode(createDTO.getAreaCode())
            .name(createDTO.getName())
            .level(createDTO.getLevel())
            .combatPower(createDTO.getCombatPower())
            .addition(createDTO.getAddition())
            .gatheringCapacity(createDTO.getGatheringCapacity())
            .build();

        save(account);

        return AccountCreateResult.success(account.getId(), account.getName(), createDTO.getAreaCode());
    }

    /**
     * 申请加入联盟
     */
    public void applyJoinCoalition(AccountJoinCoalitionDTO joinDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        // 检查账号是否属于当前用户
        SanBingAccount account = getById(joinDTO.getAccountId());
        if (account == null) {
            throw new RuntimeException("账号不存在");
        }
        if (!currentUserId.equals(account.getCreator())) {
            throw new RuntimeException("只能操作自己创建的账号");
        }
        if (account.getCoalitionId() != null) {
            throw new RuntimeException("账号已加入联盟");
        }

        // 查找联盟
        SanBingCoalition coalition = coalitionService.getByCode(joinDTO.getCoalitionCode());
        if (coalition == null) {
            throw new RuntimeException("联盟不存在");
        }

        // 检查账号和联盟是否在同一区域
        if (!account.getAreaCode().equals(coalition.getAreaCode())) {
            throw new RuntimeException("只能申请加入同区域的联盟");
        }

        // 检查联盟账号数量限制
        long accountCount = mapper.selectCountByQuery(
            QueryWrapper.create()
                .eq(SanBingAccount::getCoalitionId, coalition.getId())
                .eq(SanBingAccount::getStatus, ApproveStatus.APPROVED)
        );
        if (accountCount >= 100) {
            throw new RuntimeException("联盟账号已满，无法加入");
        }

        // 检查账号名在联盟内是否唯一
        long nameCount = mapper.selectCountByQuery(
            QueryWrapper.create()
                .eq(SanBingAccount::getCoalitionId, coalition.getId())
                .eq(SanBingAccount::getName, account.getName())
        );
        if (nameCount > 0) {
            throw new RuntimeException("联盟内已存在同名账号");
        }

        // 更新账号信息
        account.setCoalitionId(coalition.getId());
        account.setStatus(ApproveStatus.TO_BE_APPROVED);
        updateById(account);
    }

    /**
     * 审核账号申请
     */
    public void approveAccount(Long accountId, boolean approved) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingAccount account = getById(accountId);
        if (account == null) {
            throw new RuntimeException("账号不存在");
        }

        SanBingCoalition coalition = coalitionService.getById(account.getCoalitionId());
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以审核账号");
        }

        if (approved) {
            // 再次检查联盟账号数量限制
            long accountCount = mapper.selectCountByQuery(
                QueryWrapper.create()
                    .eq(SanBingAccount::getCoalitionId, coalition.getId())
                    .eq(SanBingAccount::getStatus, ApproveStatus.APPROVED)
            );
            if (accountCount >= 100) {
                throw new RuntimeException("联盟账号已满，无法通过审核");
            }

            account.setStatus(ApproveStatus.APPROVED);
        } else {
            // 拒绝申请，清空联盟信息
            account.setCoalitionId(null);
            account.setStatus(null);
        }

        updateById(account);
    }

    /**
     * 更新账号信息
     */
    public void updateAccount(AccountUpdateDTO updateDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingAccount account = getById(updateDTO.getAccountId());
        if (account == null) {
            throw new RuntimeException("账号不存在");
        }

        // 检查权限：账号创建者或联盟管理者
        boolean hasPermission = currentUserId.equals(account.getCreator());
        if (!hasPermission && account.getCoalitionId() != null) {
            SanBingCoalition coalition = coalitionService.getById(account.getCoalitionId());
            hasPermission = coalition != null && currentUserId.equals(coalition.getManagerId());
        }

        if (!hasPermission) {
            throw new RuntimeException("只有账号创建者或联盟管理者可以修改账号信息");
        }

        // 检查账号名在联盟内是否唯一（如果修改了名称）
        if (!account.getName().equals(updateDTO.getName()) && account.getCoalitionId() != null) {
            long nameCount = mapper.selectCountByQuery(
                QueryWrapper.create()
                    .eq(SanBingAccount::getCoalitionId, account.getCoalitionId())
                    .eq(SanBingAccount::getName, updateDTO.getName())
                    .ne(SanBingAccount::getId, account.getId())
            );
            if (nameCount > 0) {
                throw new RuntimeException("联盟内已存在同名账号");
            }
        }

        // 更新账号信息
        account.setName(updateDTO.getName());
        account.setLevel(updateDTO.getLevel());
        account.setCombatPower(updateDTO.getCombatPower());
        account.setAddition(updateDTO.getAddition());
        account.setGatheringCapacity(updateDTO.getGatheringCapacity());

        updateById(account);
    }

    /**
     * 移除联盟账号
     */
    public void removeAccount(Long accountId) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingAccount account = getById(accountId);
        if (account == null) {
            throw new RuntimeException("账号不存在");
        }

        if (account.getCoalitionId() == null) {
            throw new RuntimeException("账号未加入联盟");
        }

        SanBingCoalition coalition = coalitionService.getById(account.getCoalitionId());
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以移除账号");
        }

        // 清空账号的联盟信息和分组信息
        account.setCoalitionId(null);
        account.setStatus(null);
        updateById(account);

        // 清空该账号的所有分组信息
        clearAccountFromAllGroups(account.getId());
    }

    /**
     * 账号退出联盟
     */
    public void quitCoalition(Long accountId) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingAccount account = getById(accountId);
        if (account == null) {
            throw new RuntimeException("账号不存在");
        }

        if (!currentUserId.equals(account.getCreator())) {
            throw new RuntimeException("只能操作自己创建的账号");
        }

        if (account.getCoalitionId() == null) {
            throw new RuntimeException("账号未加入联盟");
        }

        // 清空账号的联盟信息和分组信息
        account.setCoalitionId(null);
        account.setStatus(null);
        updateById(account);

        // 清空该账号的所有分组信息
        clearAccountFromAllGroups(account.getId());
    }

    /**
     * 查询用户创建的账号信息
     */
    public List<AccountInfo> getUserAccounts() {
        Long currentUserId = SecurityUtils.currentUserId();

        List<SanBingAccount> accounts = mapper.selectListByQuery(
            QueryWrapper.create().eq(SanBingAccount::getCreator, currentUserId)
        );

        return accounts.stream().map(this::convertToAccountInfo).collect(Collectors.toList());
    }

    /**
     * 转换账号实体为VO
     */
    private AccountInfo convertToAccountInfo(SanBingAccount account) {
        AccountInfo info = AccountInfo.builder()
            .areaCode(account.getAreaCode())
            .accountId(account.getId())
            .name(account.getName())
            .level(account.getLevel())
            .combatPower(account.getCombatPower())
            .addition(account.getAddition())
            .gatheringCapacity(account.getGatheringCapacity())
            .status(account.getStatus())
            .build();

        // 如果账号加入了联盟，添加联盟信息
        if (account.getCoalitionId() != null) {
            SanBingCoalition coalition = coalitionService.getById(account.getCoalitionId());
            if (coalition != null) {
                info.setCoalition(CoalitionSimpleInfo.builder()
                    .coalitionId(coalition.getId())
                    .name(coalition.getName())
                    .code(coalition.getCode())
                    .build());
            }
        }

        return info;
    }

    /**
     * 清空账号的所有分组信息
     */
    private void clearAccountFromAllGroups(Long accountId) {
        // 直接使用mapper删除，避免循环依赖
        groupAccountMapper.deleteByQuery(
            QueryWrapper.create().eq(SanBingGroupAccount::getAccountId, accountId)
        );
    }
}
