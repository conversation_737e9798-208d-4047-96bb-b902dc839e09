package cn.flode.game.service;

import cn.flode.game.controller.sanbing.vo.CoalitionDetailInfo;
import cn.flode.game.controller.sanbing.vo.GroupInfo;
import cn.flode.game.controller.sanbing.vo.MemberInfo;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.entity.SanBingGroup;
import cn.flode.game.entity.SanBingGroupMember;
import cn.flode.game.entity.SanBingMember;
import cn.flode.game.entity.SanBingApplyJoinGroup;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.mapper.SanBingGroupMemberMapper;
import cn.flode.game.mapper.SanBingApplyJoinGroupMapper;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 三冰查询服务，处理复杂的查询逻辑
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingQueryService {

    private final SanBingCoalitionService coalitionService;
    private final SanBingMemberService memberService;
    private final SanBingGroupService groupService;
    private final SanBingGroupMemberMapper groupMemberMapper;
    private final SanBingApplyJoinGroupMapper applyJoinGroupMapper;

    /**
     * 查询联盟详细信息
     */
    public CoalitionDetailInfo getCoalitionDetail(Long coalitionId) {
        Long currentUserId = SecurityUtils.currentUserId();
        
        SanBingCoalition coalition = coalitionService.getById(coalitionId);
        if (coalition == null) {
            throw new RuntimeException("联盟不存在");
        }
        
        boolean isManager = currentUserId.equals(coalition.getManagerId());
        
        // 构建基本信息
        CoalitionDetailInfo.CoalitionDetailInfoBuilder builder = CoalitionDetailInfo.builder()
            .coalitionId(coalition.getId())
            .name(coalition.getName())
            .code(coalition.getCode())
            .areaCode(coalition.getAreaCode())
            .managerId(coalition.getManagerId())
            .isManager(isManager);
        
        // 查询所有分组
        List<SanBingGroup> groups = groupService.list(
            QueryWrapper.create().eq(SanBingGroup::getCoalitionId, coalitionId)
        );
        
        // 按类型分组
        Map<GroupType, List<GroupInfo>> groupsByType = new HashMap<>();
        for (GroupType type : GroupType.values()) {
            groupsByType.put(type, new ArrayList<>());
        }
        
        // 填充分组信息
        for (SanBingGroup group : groups) {
            GroupInfo groupInfo = convertToGroupInfo(group);
            groupsByType.get(group.getType()).add(groupInfo);
        }
        
        builder.groupsByType(groupsByType);
        
        // 如果是管理员，添加待审核成员信息
        if (isManager) {
            List<SanBingMember> pendingMembers = memberService.list(
                QueryWrapper.create()
                    .eq(SanBingMember::getCoalitionId, coalitionId)
                    .eq(SanBingMember::getStatus, ApproveStatus.TO_BE_APPROVED)
            );
            
            List<MemberInfo> pendingMemberInfos = pendingMembers.stream()
                .map(this::convertToMemberInfo)
                .collect(Collectors.toList());
            
            builder.pendingMembers(pendingMemberInfos);
            
            // 查询申请加入分组类型的成员信息
            List<SanBingApplyJoinGroup> applyJoinGroups = applyJoinGroupMapper.selectListByQuery(
                QueryWrapper.create().eq(SanBingApplyJoinGroup::getCoalitionId, coalitionId)
            );
            
            // 按分组类型分组申请信息
            Map<GroupType, List<MemberInfo>> applyMembersByType = new HashMap<>();
            for (GroupType type : GroupType.values()) {
                applyMembersByType.put(type, new ArrayList<>());
            }
            
            for (SanBingApplyJoinGroup apply : applyJoinGroups) {
                SanBingMember member = memberService.getById(apply.getMemberId());
                if (member != null && member.getStatus() == ApproveStatus.APPROVED) {
                    MemberInfo memberInfo = convertToMemberInfo(member);
                    applyMembersByType.get(apply.getType()).add(memberInfo);
                }
            }
            
            builder.applyMembersByType(applyMembersByType);
        }
        
        return builder.build();
    }

    /**
     * 转换分组实体为VO
     */
    private GroupInfo convertToGroupInfo(SanBingGroup group) {
        // 查询分组成员
        List<SanBingGroupMember> groupMembers = groupMemberMapper.selectListByQuery(
            QueryWrapper.create().eq(SanBingGroupMember::getGroupId, group.getId())
        );
        
        List<MemberInfo> memberInfos = new ArrayList<>();
        for (SanBingGroupMember groupMember : groupMembers) {
            SanBingMember member = memberService.getById(groupMember.getMemberId());
            if (member != null) {
                memberInfos.add(convertToMemberInfo(member));
            }
        }
        
        return GroupInfo.builder()
            .groupId(group.getId())
            .name(group.getName())
            .type(group.getType())
            .task(group.getTask())
            .members(memberInfos)
            .build();
    }

    /**
     * 转换成员实体为VO
     */
    private MemberInfo convertToMemberInfo(SanBingMember member) {
        return MemberInfo.builder()
            .memberId(member.getId())
            .name(member.getName())
            .level(member.getLevel())
            .combatPower(member.getCombatPower())
            .addition(member.getAddition())
            .gatheringCapacity(member.getGatheringCapacity())
            .status(member.getStatus())
            .build();
    }
}
