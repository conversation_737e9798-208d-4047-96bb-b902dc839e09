package cn.flode.game.service;

import cn.flode.game.controller.sanbing.dto.MemberCreateDTO;
import cn.flode.game.controller.sanbing.dto.MemberJoinCoalitionDTO;
import cn.flode.game.controller.sanbing.dto.MemberUpdateDTO;
import cn.flode.game.controller.sanbing.vo.CoalitionSimpleInfo;
import cn.flode.game.controller.sanbing.vo.MemberCreateResult;
import cn.flode.game.controller.sanbing.vo.MemberInfo;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.entity.SanBingGroupMember;
import cn.flode.game.entity.SanBingMember;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.mapper.SanBingGroupMemberMapper;
import cn.flode.game.mapper.SanBingMemberMapper;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *  服务层实现。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingMemberService extends ServiceImpl<SanBingMemberMapper, SanBingMember> {

    private final SanBingCoalitionService coalitionService;
    private final SanBingGroupMemberMapper groupMemberMapper;

    /**
     * 创建成员
     */
    public MemberCreateResult createMember(MemberCreateDTO createDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        // 检查用户是否已创建了2个成员（不限区域）
        long memberCount = mapper.selectCountByQuery(
            QueryWrapper.create().eq(SanBingMember::getCreator, currentUserId)
                .eq(SanBingMember::getAreaCode, createDTO.getAreaCode())
        );

        if (memberCount >= 2) {
            throw new RuntimeException("一个区最多只能创建2个成员");
        }

        // 创建成员
        SanBingMember member = SanBingMember.builder()
            .areaCode(createDTO.getAreaCode())
            .name(createDTO.getName())
            .level(createDTO.getLevel())
            .combatPower(createDTO.getCombatPower())
            .addition(createDTO.getAddition())
            .gatheringCapacity(createDTO.getGatheringCapacity())
            .build();

        save(member);

        return MemberCreateResult.success(member.getId(), member.getName(), createDTO.getAreaCode());
    }

    /**
     * 申请加入联盟
     */
    public void applyJoinCoalition(MemberJoinCoalitionDTO joinDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        // 检查成员是否属于当前用户
        SanBingMember member = getById(joinDTO.getMemberId());
        if (member == null) {
            throw new RuntimeException("成员不存在");
        }
        if (!currentUserId.equals(member.getCreator())) {
            throw new RuntimeException("只能操作自己创建的成员");
        }
        if (member.getCoalitionId() != null) {
            throw new RuntimeException("成员已加入联盟");
        }

        // 查找联盟
        SanBingCoalition coalition = coalitionService.getByCode(joinDTO.getCoalitionCode());
        if (coalition == null) {
            throw new RuntimeException("联盟不存在");
        }

        // 检查成员和联盟是否在同一区域
        if (!member.getAreaCode().equals(coalition.getAreaCode())) {
            throw new RuntimeException("只能申请加入同区域的联盟");
        }

        // 检查联盟成员数量限制
        long memberCount = mapper.selectCountByQuery(
            QueryWrapper.create()
                .eq(SanBingMember::getCoalitionId, coalition.getId())
                .eq(SanBingMember::getStatus, ApproveStatus.APPROVED)
        );
        if (memberCount >= 100) {
            throw new RuntimeException("联盟成员已满，无法加入");
        }

        // 检查成员名在联盟内是否唯一
        long nameCount = mapper.selectCountByQuery(
            QueryWrapper.create()
                .eq(SanBingMember::getCoalitionId, coalition.getId())
                .eq(SanBingMember::getName, member.getName())
        );
        if (nameCount > 0) {
            throw new RuntimeException("联盟内已存在同名成员");
        }

        // 更新成员信息
        member.setCoalitionId(coalition.getId());
        member.setStatus(ApproveStatus.TO_BE_APPROVED);
        updateById(member);
    }

    /**
     * 审核成员申请
     */
    public void approveMember(Long memberId, boolean approved) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingMember member = getById(memberId);
        if (member == null) {
            throw new RuntimeException("成员不存在");
        }

        SanBingCoalition coalition = coalitionService.getById(member.getCoalitionId());
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以审核成员");
        }

        if (approved) {
            // 再次检查联盟成员数量限制
            long memberCount = mapper.selectCountByQuery(
                QueryWrapper.create()
                    .eq(SanBingMember::getCoalitionId, coalition.getId())
                    .eq(SanBingMember::getStatus, ApproveStatus.APPROVED)
            );
            if (memberCount >= 100) {
                throw new RuntimeException("联盟成员已满，无法通过审核");
            }

            member.setStatus(ApproveStatus.APPROVED);
        } else {
            // 拒绝申请，清空联盟信息
            member.setCoalitionId(null);
            member.setStatus(null);
        }

        updateById(member);
    }

    /**
     * 更新成员信息
     */
    public void updateMember(MemberUpdateDTO updateDTO) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingMember member = getById(updateDTO.getMemberId());
        if (member == null) {
            throw new RuntimeException("成员不存在");
        }

        // 检查权限：成员创建者或联盟管理者
        boolean hasPermission = currentUserId.equals(member.getCreator());
        if (!hasPermission && member.getCoalitionId() != null) {
            SanBingCoalition coalition = coalitionService.getById(member.getCoalitionId());
            hasPermission = coalition != null && currentUserId.equals(coalition.getManagerId());
        }

        if (!hasPermission) {
            throw new RuntimeException("只有成员创建者或联盟管理者可以修改成员信息");
        }

        // 检查成员名在联盟内是否唯一（如果修改了名称）
        if (!member.getName().equals(updateDTO.getName()) && member.getCoalitionId() != null) {
            long nameCount = mapper.selectCountByQuery(
                QueryWrapper.create()
                    .eq(SanBingMember::getCoalitionId, member.getCoalitionId())
                    .eq(SanBingMember::getName, updateDTO.getName())
                    .ne(SanBingMember::getId, member.getId())
            );
            if (nameCount > 0) {
                throw new RuntimeException("联盟内已存在同名成员");
            }
        }

        // 更新成员信息
        member.setName(updateDTO.getName());
        member.setLevel(updateDTO.getLevel());
        member.setCombatPower(updateDTO.getCombatPower());
        member.setAddition(updateDTO.getAddition());
        member.setGatheringCapacity(updateDTO.getGatheringCapacity());

        updateById(member);
    }

    /**
     * 移除联盟成员
     */
    public void removeMember(Long memberId) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingMember member = getById(memberId);
        if (member == null) {
            throw new RuntimeException("成员不存在");
        }

        if (member.getCoalitionId() == null) {
            throw new RuntimeException("成员未加入联盟");
        }

        SanBingCoalition coalition = coalitionService.getById(member.getCoalitionId());
        if (coalition == null || !currentUserId.equals(coalition.getManagerId())) {
            throw new RuntimeException("只有联盟管理者可以移除成员");
        }

        // 清空成员的联盟信息和分组信息
        member.setCoalitionId(null);
        member.setStatus(null);
        updateById(member);

        // 清空该成员的所有分组信息
        clearMemberFromAllGroups(member.getId());
    }

    /**
     * 成员退出联盟
     */
    public void quitCoalition(Long memberId) {
        Long currentUserId = SecurityUtils.currentUserId();

        SanBingMember member = getById(memberId);
        if (member == null) {
            throw new RuntimeException("成员不存在");
        }

        if (!currentUserId.equals(member.getCreator())) {
            throw new RuntimeException("只能操作自己创建的成员");
        }

        if (member.getCoalitionId() == null) {
            throw new RuntimeException("成员未加入联盟");
        }

        // 清空成员的联盟信息和分组信息
        member.setCoalitionId(null);
        member.setStatus(null);
        updateById(member);

        // 清空该成员的所有分组信息
        clearMemberFromAllGroups(member.getId());
    }

    /**
     * 查询用户创建的成员信息
     */
    public List<MemberInfo> getUserMembers() {
        Long currentUserId = SecurityUtils.currentUserId();

        List<SanBingMember> members = mapper.selectListByQuery(
            QueryWrapper.create().eq(SanBingMember::getCreator, currentUserId)
        );

        return members.stream().map(this::convertToMemberInfo).collect(Collectors.toList());
    }

    /**
     * 转换成员实体为VO
     */
    private MemberInfo convertToMemberInfo(SanBingMember member) {
        MemberInfo info = MemberInfo.builder()
            .areaCode(member.getAreaCode())
            .memberId(member.getId())
            .name(member.getName())
            .level(member.getLevel())
            .combatPower(member.getCombatPower())
            .addition(member.getAddition())
            .gatheringCapacity(member.getGatheringCapacity())
            .status(member.getStatus())
            .build();

        // 如果成员加入了联盟，添加联盟信息
        if (member.getCoalitionId() != null) {
            SanBingCoalition coalition = coalitionService.getById(member.getCoalitionId());
            if (coalition != null) {
                info.setCoalition(CoalitionSimpleInfo.builder()
                    .coalitionId(coalition.getId())
                    .name(coalition.getName())
                    .code(coalition.getCode())
                    .build());
            }
        }

        return info;
    }

    /**
     * 清空成员的所有分组信息
     */
    private void clearMemberFromAllGroups(Long memberId) {
        // 直接使用mapper删除，避免循环依赖
        groupMemberMapper.deleteByQuery(
            QueryWrapper.create().eq(SanBingGroupMember::getMemberId, memberId)
        );
    }
}
